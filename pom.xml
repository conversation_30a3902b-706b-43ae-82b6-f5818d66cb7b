<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>me.simplicitee</groupId>
    <artifactId>projectaddons</artifactId>
    <version>1.0.5</version>
    <packaging>jar</packaging>
    <name>ProjectAddons</name>

    <repositories>
        <repository>
            <id>papermc</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
        <repository>
            <id>tlb-repo</id>
            <url>https://repo.thelastblockbender.com/private</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- Spigot API -->
        <dependency>
            <groupId>io.papermc.paper</groupId>
            <artifactId>paper-api</artifactId>
            <version>1.21.4-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <!-- ProjectKorra -->
        <dependency>
            <groupId>com.projectkorra</groupId>
            <artifactId>projectkorra</artifactId>
            <version>1.9.4-build.1</version>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>clean package install</defaultGoal>
        <finalName>${project.name}-${project.version}</finalName>
        <sourceDirectory>${project.basedir}/src/</sourceDirectory>
        <resources>
            <resource>
                <targetPath>.</targetPath>
                <filtering>true</filtering>
                <directory>${project.basedir}/src/</directory>
                <includes>
                    <include>*.yml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <outputDirectory>${dir}</outputDirectory>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <dir>${project.build.directory}</dir>
    </properties>
</project>
