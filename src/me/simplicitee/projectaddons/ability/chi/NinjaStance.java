package me.simplicitee.projectaddons.ability.chi;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class NinjaStance extends ChiAbility implements AddonAbility{
	
	private boolean stealth, stealthReady, stealthStarted;
  private long stealthStart;
  private long stealthChargeTime;
  private long stealthReadyStart;
  private long stealthDuration;
  private long cooldown;
  private int speedAmp, jumpAmp;
  private double prevHealth;
  private double dmgModifier;

	public NinjaStance(Player player) {
		super(player);
		ChiAbility stance = bPlayer.getStance();
		if (stance != null) {
			stance.remove();
			if (stance instanceof NinjaStance) {
				bPlayer.setStance(null);
				return;
			}
		}

    setFields();
		start();
		bPlayer.setStance(this);
		GeneralMethods.displayMovePreview(player);
		player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_HURT, 0.2F, 2F);
	}
	
	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    stealthDuration = TLBPAMethods.getLong("Abilities.NinjaStance.Stealth.Duration", currentLevel);
    stealthChargeTime = TLBPAMethods.getLong("Abilities.NinjaStance.Stealth.ChargeTime", currentLevel);
    speedAmp = TLBPAMethods.getInt("Abilities.NinjaStance.SpeedAmplifier", currentLevel) - 1;
    jumpAmp = TLBPAMethods.getInt("Abilities.NinjaStance.JumpAmplifier", currentLevel) - 1;
    cooldown = TLBPAMethods.getLong("Abilities.NinjaStance.Cooldown", currentLevel);
    dmgModifier = TLBPAMethods.getDouble("Abilities.NinjaStance.DamageModifier", currentLevel);
  }

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return player.getLocation();
	}

	@Override
	public String getName() {
		return "NinjaStance";
	}

	@Override
	public boolean isHarmlessAbility() {
		return true;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead()) {
			remove();
			return;
		}
		
		if (stealth) {
			if (System.currentTimeMillis() >= stealthStart + stealthChargeTime) {
				stealthReady = true;
			}
			
			if (!stealthStarted) {
				if (stealthReady && !player.isSneaking()) {
					stealthReadyStart = System.currentTimeMillis();
					stealthStarted = true;
				} else if (!player.isSneaking()) {
					stopStealth();
				} else if (stealthReady && player.isSneaking()) {
					Location play = player.getEyeLocation().clone().add(player.getEyeLocation().getDirection().normalize());
					GeneralMethods.displayColoredParticle("#00ee00", play);
				} else {
					Location play = player.getEyeLocation().clone().add(player.getEyeLocation().getDirection().normalize());
					GeneralMethods.displayColoredParticle("#000000", play);
				}
			} else {
				if (System.currentTimeMillis() >= stealthReadyStart + stealthDuration) {
					this.bPlayer.addCooldown(this);
					stopStealth();
				} else {
					player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 5, 2, true, false), true);
				}
			}
		}
		
		if (prevHealth > player.getHealth()) {
			remove();
			stopStealth();
			this.bPlayer.addCooldown(this);
			return;
		}

		prevHealth = player.getHealth();
		
		
		//player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 5, speedAmp, true, false), true);
		//player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP, 5, jumpAmp, true, false), true);
	}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}
	
	@Override
	public String getDescription() {
		return "This stance allows chiblockers to become more stealthy (like a ninja)!";
	}
	
	@Override
	public String getInstructions() {
		return "Left click to begin to this stance > Hold sneak to begin stealth mode";
	}
	
	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.NinjaStance.Enabled");
	}

	public void beginStealth() {
		if (stealth) {
			player.sendMessage("Already cloaked!");
			return;
		}
		stealth = true;
		stealthStart = System.currentTimeMillis();
	}
	
	public void stopStealth() {
    if (stealth && stealthReady && player.hasPotionEffect(PotionEffectType.INVISIBILITY)) {
      stealth = false;
      stealthReady = false;
      stealthStarted = false;
    }
	}
	
	public double getDamageModifier() {
		return dmgModifier;
	}
}
