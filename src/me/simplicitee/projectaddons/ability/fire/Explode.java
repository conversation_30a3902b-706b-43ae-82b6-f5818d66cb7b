package me.simplicitee.projectaddons.ability.fire;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.CombustionAbility;
import com.projectkorra.projectkorra.ability.ElementalAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class Explode extends CombustionAbility implements AddonAbility {
	
	private long cooldown;
	private double damage;
	private double radius;
	private double knockback;
	private double range;
	private Location center;

	public Explode(Player player) {
		super(player);
    setFields();
		start();
	}

	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBPAMethods.getLong("Abilities.Explode.Cooldown", currentLevel);
    this.damage = TLBPAMethods.getDouble("Abilities.Explode.Damage", currentLevel);
    this.radius = TLBPAMethods.getDouble("Abilities.Explode.Radius", currentLevel);
    this.knockback = TLBPAMethods.getDouble("Abilities.Explode.Knockback", currentLevel);
    this.range = TLBPAMethods.getDouble("Abilities.Explode.Range", currentLevel);
	}
	
	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead()) {
			remove();
			return;
		}
		
		if (!bPlayer.canBendIgnoreBinds(this)) {
			remove();
			return;
		}
		
		if (player.isSneaking()) {
			this.center = GeneralMethods.getTargetedLocation(player, range, ElementalAbility.getTransparentMaterials());
			
			//ParticleEffect.FLAME.display(center, 1);
			playFirebendingParticles(center, 1, 0, 0, 0);
			ParticleEffect.CRIT.display(center, 3, 0.3, 0.3, 0.3);
			player.getWorld().playSound(center, Sound.ENTITY_CREEPER_PRIMED, 0.2f, 8f);
		} else {
			if (center != null) {
				double offset = radius / 2;
				playFirebendingParticles(center, 7, offset, offset, offset);
				//ParticleEffect.FLAME.display(center, 7, offset, offset, offset);
				ParticleEffect.CRIT.display(center, 6, offset, offset, offset);
				ParticleEffect.EXPLOSION_HUGE.display(center, 1);
				player.getWorld().playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 2f, 3f);
				
				for (Entity e : GeneralMethods.getEntitiesAroundPoint(center, radius)) {
					if (e instanceof LivingEntity) {
						Vector direction = GeneralMethods.getDirection(center, ((LivingEntity) e).getEyeLocation()).normalize().multiply(knockback);
						DamageHandler.damageEntity(e, damage, this);
						e.setVelocity(direction);
					}
				}
				
				remove();
			}
			remove();
		}
	}
	
	@Override
	public void remove() {
		super.remove();
		bPlayer.addCooldown(this);
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "Explode";
	}

	@Override
	public Location getLocation() {
		return center;
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}
	
	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.Explode.Enabled");
	}

	@Override
	public String getDescription() {
		return "Cause a spontaneous explosion where you are looking! Big boom!";
	}
	
	@Override
	public String getInstructions() {
		return "Hold sneak to aim, release sneak to explode!";
	}
}
