package me.simplicitee.projectaddons.ability.earth;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class Dig extends EarthAbility implements AddonAbility {
	private long cooldown;
	private long duration;
	private long revertTime;
	private double speed;
	private double radius;
	
	public Dig(Player player) {
		super(player);
		
		if (!canDig(player.getLocation().getBlock().getRelative(BlockFace.DOWN))) {
			return;
		}
		
		setFields();
    Bukkit.getConsoleSender().sendMessage("r= " + radius);
		start();
	}
	
	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);
		
		this.cooldown = TLBPAMethods.getLong("Abilities.Dig.Cooldown", currentLevel);
		this.duration = TLBPAMethods.getLong("Abilities.Dig.Duration", currentLevel);
		this.revertTime = TLBPAMethods.getLong("Abilities.Dig.RevertTime", currentLevel);
		this.speed = TLBPAMethods.getDouble("Abilities.Dig.Speed", currentLevel);
		this.radius = TLBPAMethods.getDouble("Abilities.Dig.Radius", currentLevel);
	}

	@Override
	public void progress() {
		if (!bPlayer.canBend(this) || !player.isSneaking()) {
			remove();
			return;
		}
		
		if (duration > 0 && System.currentTimeMillis() > getStartTime() + duration) {
			remove();
			return;
		}
		
		Block b = player.getTargetBlock(getTransparentMaterialSet(), 4);
		if (!canDig(b)) {
			player.setVelocity(player.getEyeLocation().getDirection().multiply(0.9));
			remove();
			return;
		}
		
		for (Block block : GeneralMethods.getBlocksAroundPoint(player.getEyeLocation(), this.radius)) {
			if (canDig(block)) {
				ParticleEffect.BLOCK_CRACK.display(block.getLocation().add(0.5, 0.5, 0.5), 5, 0.25, 0.25, 0.25, block.getBlockData());
				new TempBlock(block, Material.AIR.createBlockData(), revertTime);
			}
		}
		
		ParticleEffect.CRIT.display(player.getEyeLocation(), 7, 0.6, 0.6, 0.6);
		
		player.setGliding(true);
		player.setVelocity(player.getEyeLocation().getDirection().multiply(speed));
		player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 5, 1, false, false));
	}

  private boolean canDig(Block block) {
    if (getPreventEarthbendingBlocks().contains(block) || GeneralMethods.isRegionProtectedFromBuild(this, block.getLocation())) {
      return false;
    }
    return isEarthbendable(block.getType(), false, bPlayer.canSandbend(), false);
  }

	@Override
	public void remove() {
		super.remove();
		bPlayer.addCooldown(this);
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return true;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "Dig";
	}

	@Override
	public Location getLocation() {
		return player.getEyeLocation();
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.Dig.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "Swim through the earth, digging a path with your earthbending. Inspired by toph's learning from the badgermoles! You must also be looking at an earthbendable block for the ability to work!";
	}
	
	@Override
	public String getInstructions() {
		return "Sneak while on an earthbendable block";
	}
}
