package me.simplicitee.projectaddons.ability.earth;

import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import me.simplicitee.projectaddons.TLBPAMethods;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.TempBlock;

import me.simplicitee.projectaddons.ProjectAddons;

public class Crumble extends SandAbility implements AddonAbility {
	private int selectRange, radius, maxRadius, counter;
	private long cooldown, revertTime;
	private Block center;

	public Crumble(Player player, ClickType click) {
		super(player);
		
		if (bPlayer.isOnCooldown(this)) {
			return;
		}

    setFields();
		if (click == ClickType.LEFT_CLICK) {
			this.center = player.getTargetBlock(null, selectRange);
		} else {
			this.center = player.getLocation().getBlock().getRelative(BlockFace.DOWN);
		}
		this.counter = 0;
		this.radius = 0;
		start();
	}

  private void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.selectRange = TLBPAMethods.getInt("Abilities.Crumble.SelectRange", currentLevel);
    this.maxRadius = TLBPAMethods.getInt("Abilities.Crumble.Radius", currentLevel);
    this.revertTime = TLBPAMethods.getLong("Abilities.Crumble.RevertTime", currentLevel);
    this.cooldown = TLBPAMethods.getLong("Abilities.Crumble.Cooldown", currentLevel);
  }

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public String getName() {
		return "Crumble";
	}

	@Override
	public boolean isHarmlessAbility() {
		return true;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public void progress() {
		if (!bPlayer.canBend(this) || radius > maxRadius) {
			remove();
			return;
		}
		
		counter++;
		if (counter % 2 != 0) {
			return;
		}
		
		for (int theta = 0; theta < 360; theta += 5) {
			double x = Math.cos(Math.toRadians(theta)) * radius;
			double z = Math.sin(Math.toRadians(theta)) * radius;
			
			Block block = center.getRelative((int)x, 0, (int)z);
			block = GeneralMethods.getTopBlock(block.getLocation(), 2);
			
			if (block.isPassable() && !block.isLiquid()) {
				block.breakNaturally();
				block = block.getRelative(BlockFace.DOWN);
			}
			
			if (TempBlock.isTempBlock(block)) {
				continue;
			} else if (!isEarthbendable(block)) {
				continue;
			} else if (isSand(block)) {
				continue;
			}
			
			Material m = Material.SAND;
			if (isAir(block.getRelative(BlockFace.DOWN).getType())) {
				m = Material.SANDSTONE;
			}
			new TempBlock(block, m.createBlockData(), 20 * revertTime);
		}
		radius++;
	}
	
	@Override
	public void remove() {
		super.remove();
		bPlayer.addCooldown(this);
	}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public void load() {
	}

	@Override
	public void stop() {
	}
	
	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.Crumble.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "Crumble the earth into sand!";
	}
	
	@Override
	public String getInstructions() {
		return "Left click or sneak";
	}
}
