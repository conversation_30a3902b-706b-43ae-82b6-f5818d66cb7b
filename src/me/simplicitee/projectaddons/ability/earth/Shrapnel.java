package me.simplicitee.projectaddons.ability.earth;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Item;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.MetalAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class Shrapnel extends MetalAbility implements AddonAbility {
  private final Collection<ShrapnelShot> shots = new ArrayList<>();
  private ShotData data;
  private long cooldown;

	public Shrapnel(Player player, boolean blast) {
		super(player);

    if (!bPlayer.canBend(this)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    String configSection = "Abilities.Shrapnel." + (blast ? "Blast" : "Shot");
    setFields(configSection, currentLevel);

    if (blast) {
      int amount = TLBPAMethods.getInt("Abilities.Shrapnel.Blast.Shots", currentLevel);
      int spread = TLBPAMethods.getInt("Abilities.Shrapnel.Blast.Spread", currentLevel);
      for (int i = 0; i < amount; i++) {
        Location loc = player.getLocation().clone();
        int yaw = new Random().nextInt(spread/2) - spread/4;
        loc.setYaw(loc.getYaw() + yaw);
        int pitch = new Random().nextInt(spread/2) - spread/4;
        loc.setPitch(loc.getPitch() + pitch);
        tryLaunch(loc.getDirection());
      }
    } else {
      tryLaunch(player.getLocation().getDirection());
    }

    if (!shots.isEmpty()) {
      bPlayer.addCooldown(this);
      start();
    }
  }

  private void setFields(String section, long currentLevel) {
    cooldown = TLBPAMethods.getLong(section + ".Cooldown", currentLevel);
    double damage = TLBPAMethods.getDouble(section + ".Damage", currentLevel);
    double range = TLBPAMethods.getDouble(section + ".Range", currentLevel);
    double speed = TLBPAMethods.getDouble(section + ".Speed", currentLevel);
    double collisionRadius = TLBPAMethods.getDouble(section + ".CollisionRadius", currentLevel);
    data = new ShotData(damage, range, speed, collisionRadius);
  }

  private void tryLaunch(Vector direction) {
    Material m;
    if (player.getInventory().contains(Material.IRON_NUGGET)) {
      m = Material.IRON_NUGGET;
    } else if (player.getInventory().contains(Material.GOLD_NUGGET)) {
      m = Material.GOLD_NUGGET;
    } else {
      return;
    }
    int slot = player.getInventory().first(m);
    ItemStack is = player.getInventory().getItem(slot);
    player.getInventory().setItem(slot, is.subtract());

    Location spawn = GeneralMethods.getRightSide(player.getLocation(), 0.12).add(0, 1.3, 0);

    Item projectile = player.getWorld().dropItem(spawn, new ItemStack(m), e -> {
      e.setMetadata("shrapnel", new FixedMetadataValue(ProjectKorra.plugin, 0));
      final UUID uuid = player.getUniqueId();
      e.setOwner(uuid);
      e.setThrower(uuid);
      e.setCanPlayerPickup(false);
      e.setCanMobPickup(false);
      e.setPersistent(false);
      e.setVelocity(direction.add(new Vector(0, 0.105, 0)).normalize().multiply(data.speed));
    });

    shots.add(new ShrapnelShot(projectile, this));
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return null;
  }

  @Override
  public List<Location> getLocations() {
    return shots.stream().map(ShrapnelShot::location).toList();
  }

  @Override
  public String getName() {
    return "Shrapnel";
  }

  @Override
  public void progress() {
    if (shots.isEmpty() || !player.isOnline() || player.isDead()) {
      remove();
      return;
    }
    shots.removeIf(s -> !s.progress());
  }

  @Override
  public void remove() {
    shots.forEach(ShrapnelShot::allowPickup);
    super.remove();
  }

  @Override
  public String getAuthor() {
    return "Simplicitee";
  }

  @Override
  public String getVersion() {
    return ProjectAddons.instance.version();
  }

  @Override
  public String getDescription() {
    return "Use your metalbending to throw nuggets of gold and iron like pieces of shrapnel, dealing damage when they hit entities. This requires that you have gold or iron nuggets in your inventory to launch!";
  }

  @Override
  public String getInstructions() {
    return "Click to shoot a single piece of shrapnel at high velocity to the targeted location, click while sneaking to launch several shotgun-style.";
  }

  @Override
  public void load() {}

  @Override
  public void stop() {}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.Shrapnel.Enabled");
	}

  private record ShotData(double dmg, double range, double speed, double collisionRadius) {}

  private static class ShrapnelShot {
    private final Player player;
    private final Ability parent;
    private final ShotData data;
    private final Item projectile;

    private ShrapnelShot(Item projectile, Shrapnel parent) {
      this.projectile = projectile;
      this.player = parent.player;
      this.data = parent.data;
      this.parent = parent;
    }

    private boolean progress() {
      if (projectile.isDead() || projectile.isOnGround()) {
        allowPickup();
        return false;
      }
      Location loc = location();
      if (!player.getWorld().equals(projectile.getWorld()) || loc.distanceSquared(player.getLocation()) > data.range * data.range) {
        projectile.setVelocity(new Vector(0, 0, 0));
        allowPickup();
        return false;
      }

      ParticleEffect.CRIT.display(loc, 1);
      player.getWorld().playSound(loc, Sound.ITEM_TRIDENT_HIT, 0.2f, 1f);

      for (Entity e : GeneralMethods.getEntitiesAroundPoint(loc, data.collisionRadius)) {
        if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId()) {
          player.getWorld().playSound(e.getLocation(), Sound.BLOCK_ANVIL_PLACE, 0.4f, 1f);
          DamageHandler.damageEntity(e, player, data.dmg, parent);
          projectile.remove();
          return false;
        }
      }
      return true;
    }

    private void allowPickup() {
      if (projectile.isValid()) {
        projectile.setCanPlayerPickup(true);
      }
    }

    private Location location() {
      return projectile.getLocation();
    }
  }
}
