package me.simplicitee.projectaddons.ability.earth;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Entity;
import org.bukkit.entity.FallingBlock;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class Accretion extends EarthAbility implements AddonAbility {
	
	private boolean shot;
	private double damage;
	private int blocks;
	private int selectRange;
	private long revertTime;
	private long cooldown;
	private Set<FallingBlock> tracker;
	private Set<TempBlock> temps;

	public Accretion(Player player) {
		super(player);
		
		if (bPlayer.isOnCooldown(this)) {
			return;
		}
		
		if (!isEarthbendable(player.getLocation().getBlock().getRelative(BlockFace.DOWN))) {
			return;
		}
		
		if (hasAbility(player, Accretion.class)) {
			return;
		}
		
		if (GeneralMethods.isRegionProtectedFromBuild(this, player.getLocation())) {
			return;
		}

		this.shot = false;
    setFields();

		this.tracker = new HashSet<>();
		this.temps = new HashSet<>();
		
		List<Location> list = GeneralMethods.getCircle(player.getLocation(), selectRange, 1, false, false, 0);
		
		for (int i = 0; i < list.size(); i++) {
			Block b = GeneralMethods.getTopBlock(list.get(new Random().nextInt(list.size())), 2);
			
			if (!isAir(b.getRelative(BlockFace.UP).getType())) {
				continue;
			}
			
			if (TempBlock.isTempBlock(b)) {
				continue;
			}
			
			if (!isEarthbendable(b)) {
				continue;
			}
			
			Material type = b.getType();
			temps.add(new TempBlock(b, Material.AIR));
			FallingBlock fb = GeneralMethods.spawnFallingBlock(b.getLocation().add(0.5, 0.5, 0.5), type);
			
			fb.setVelocity(new Vector(0, 0.8, 0));
			fb.setMetadata("accretion", new FixedMetadataValue(ProjectAddons.instance, this));
			fb.setHurtEntities(false);
			fb.setDropItem(false);
			
			tracker.add(fb);
			
			if (temps.size() == blocks) {
				break;
			}
		}
		
		playEarthbendingSound(player.getLocation());
		start();
	}
	

	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.damage = TLBPAMethods.getDouble("Abilities.Accretion.Damage", currentLevel);
    this.blocks = TLBPAMethods.getInt("Abilities.Accretion.Blocks", currentLevel);
    this.selectRange = TLBPAMethods.getInt("Abilities.Accretion.SelectRange", currentLevel);
    this.revertTime = TLBPAMethods.getLong("Abilities.Accretion.RevertTime", currentLevel);
    this.cooldown = TLBPAMethods.getLong("Abilities.Accretion.Cooldown", currentLevel);
	}

	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead()) {
			remove();
			return;
		}
		
		List<FallingBlock> remove = new ArrayList<>();
		
		loop: for (FallingBlock fb : tracker) {
			ParticleEffect.BLOCK_CRACK.display(fb.getLocation(), 2, 0.1, 0.1, 0.1, fb.getBlockData());
			
			if (shot) {
				for (Entity e : GeneralMethods.getEntitiesAroundPoint(fb.getLocation(), 1)) {
					if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId()) {
						entityCollision(fb, (LivingEntity) e);
						remove.add(fb);
						continue loop;
					}
				}
			}
		}
		
		tracker.removeAll(remove);
		
		if (tracker.isEmpty()) {
			remove();
			return;
		}
		
		if (System.currentTimeMillis() - getStartTime() >= 6000) {
			for (FallingBlock fb : tracker) {
				fb.remove();
			}
			remove();
			return;
		}
	}
	
	@Override
	public void remove() {
		super.remove();
		tracker.clear();
		
		for (TempBlock tb : temps) {
			if (tb.getBlockData().getMaterial() == Material.AIR) {
				tb.revertBlock();
			}
		}
		
		temps.clear();
	}
	
	public void entityCollision(FallingBlock fb, LivingEntity entity) {
		int duration = 10;
		int amp = 1;
		if (entity.hasPotionEffect(PotionEffectType.SLOWNESS)) {
			PotionEffect effect = entity.getPotionEffect(PotionEffectType.SLOWNESS);
			
			duration += effect.getDuration();
			amp += effect.getAmplifier();
			
			entity.removePotionEffect(PotionEffectType.SLOWNESS);
		}
		
		entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, amp, true, false));
		DamageHandler.damageEntity(entity, damage, this);
		fb.remove();
	}
	
	public void blockCollision(FallingBlock fb, Block block) {
		TempBlock tb;
		if (TempBlock.isTempBlock(block)) {
			tb = TempBlock.get(block);
			tb.setType(fb.getBlockData());
		} else {
			tb = new TempBlock(block, fb.getBlockData().getMaterial());
		}
		
		tb.setRevertTime(revertTime);
		
		temps.add(tb);
		tracker.remove(fb);
		fb.remove();
	}
	
	public void shoot() {
		if (shot) {
			return;
		}
		
		if (tracker.isEmpty()) {
			remove();
			return;
		}
		
		playEarthbendingSound(player.getLocation());
		
		for (FallingBlock fb : tracker) {
			Location target = null;
			Entity e = GeneralMethods.getTargetedEntity(player, 30);
			
			if (e != null) {
				target = e.getLocation();
			} else {
				target = GeneralMethods.getTargetedLocation(player, 30);
			}
			
			fb.setVelocity(GeneralMethods.getDirection(fb.getLocation(), target).normalize().multiply(1.5));
		}
		
		bPlayer.addCooldown(this);
		shot = true;
	}

	@Override
	public boolean isSneakAbility() {
		return false;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "Accretion";
	}

	@Override
	public Location getLocation() {
		return player.getLocation();
	}

	@Override
	public void load() {
	}

	@Override
	public void stop() {
	}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.Accretion.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "Slam the earth to send blocks into the air, then shoot them all towards a single point! They will build up on an enemy, damaging and slowing them down! Each block that hits adds 1 second and level of slowness.";
	}
	
	@Override
	public String getInstructions() {
		return "Sneak to rise blocks, Left Click before they land to shoot!";
	}
}
