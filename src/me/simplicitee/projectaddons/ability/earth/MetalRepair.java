package me.simplicitee.projectaddons.ability.earth;

import java.util.EnumSet;
import java.util.Set;

import me.simplicitee.projectaddons.TLBPAMethods;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.MetalAbility;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import org.bukkit.inventory.meta.Damageable;
import org.bukkit.util.NumberConversions;

public class MetalRepair extends MetalAbility implements AddonAbility{
  private ItemStack item;
	private long cooldown;
	private double repairAmount;
	private long repairCooldown;
  private long nextRepairTime;

	private static final Set<Material> IRON = EnumSet.of(
			Material.IRON_AXE, Material.IRON_BOOTS, Material.IRON_CHESTPLATE,
			Material.IRON_HELMET, Material.IRON_HOE, Material.IRON_LEGGINGS,
			Material.IRON_PICKAXE, Material.IRON_SHOVEL, Material.IRON_SWORD, Material.SHEARS
  );
	
	private static final Set<Material> GOLD = EnumSet.of(
			Material.GOLDEN_AXE, Material.GOLDEN_BOOTS, Material.GOLDEN_CHESTPLATE,
			Material.GOLDEN_HELMET, Material.GOLDEN_HOE, Material.GOLDEN_LEGGINGS,
			Material.GOLDEN_PICKAXE, Material.GOLDEN_SHOVEL, Material.GOLDEN_SWORD
  );
	
	private static final Set<Material> NETHERITE = EnumSet.of(
			Material.NETHERITE_AXE, Material.NETHERITE_BOOTS, Material.NETHERITE_CHESTPLATE,
			Material.NETHERITE_HELMET, Material.NETHERITE_HOE, Material.NETHERITE_LEGGINGS,
			Material.NETHERITE_PICKAXE, Material.NETHERITE_SHOVEL, Material.NETHERITE_SWORD
  );

  public MetalRepair(Player player, ItemStack item) {
		super(player);
    if (item.getItemMeta() instanceof Damageable) {
      this.item = item;
      setFields();
      start();
    }
	}
	
	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBPAMethods.getLong("Abilities.MetalRepair.Cooldown", currentLevel);
    this.repairAmount = TLBPAMethods.getDouble("Abilities.MetalRepair.RepairAmount", currentLevel);
    this.repairCooldown = TLBPAMethods.getLong("Abilities.MetalRepair.RepairInterval", currentLevel);
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return player.getLocation();
	}

	@Override
	public String getName() {
		return "MetalRepair";
	}

	@Override
	public boolean isHarmlessAbility() {
		return true;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public void progress() {
		if (!bPlayer.canBend(this) || !player.isSneaking()) {
			remove();
			return;
		}
    long time = System.currentTimeMillis();
		if (time > nextRepairTime) {
      nextRepairTime = time + repairCooldown;
      Material type = item.getType();
      int amount = NumberConversions.round(type.getMaxDurability() / repairAmount);
      Bukkit.getConsoleSender().sendMessage(type.getMaxDurability() + " / " + repairAmount + " ~= " + amount);
      if (IRON.contains(type)) {
        handleItem(Material.IRON_INGOT, amount);
      } else if (GOLD.contains(type)) {
        handleItem(Material.GOLD_INGOT, amount);
      } else if (NETHERITE.contains(type)) {
        handleItem(Material.NETHERITE_INGOT, amount);
      } else {
        remove();
      }
		}
	}

  private void handleItem(Material material, int amount) {
    ItemStack stack = new ItemStack(material, 1);
    if (item.getItemMeta() instanceof Damageable meta && player.getInventory().containsAtLeast(stack, 1)) {
      int damage = meta.getDamage();
      if (damage <= 0) {
        remove();
        return;
      }
      player.getInventory().removeItem(stack);
      int newDamage = Math.max(0, damage - amount);
      meta.setDamage(newDamage);
      item.setItemMeta(meta);
      player.getWorld().playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 0.5f, 1f);
      if (newDamage == 0) {
        remove();
      }
    } else {
      remove();
    }
  }

	@Override
	public void remove() {
		super.remove();
		bPlayer.addCooldown(this);
	}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public void load() {}
	
	@Override
	public String getDescription() {
		return "Advanced metalbenders can use this to repair damaged iron weapons/armor/tools. This ability requires iron ingots in your inventory to work.";
	}
	
	@Override
	public String getInstructions() {
		return "Sneak with the item you want to repair in your main hand.";
	}

	@Override
	public void stop() {}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.MetalRepair.Enabled");
	}
}
