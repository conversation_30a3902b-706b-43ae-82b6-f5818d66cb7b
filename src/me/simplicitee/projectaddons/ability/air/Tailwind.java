package me.simplicitee.projectaddons.ability.air;

import java.util.ArrayList;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class Tailwind extends AirAbility implements ComboAbility, AddonAbility {
	private long cooldown;
	private long duration;
	private int speed;

	public Tailwind(Player player) {
		super(player);
		
		if (!bPlayer.canBendIgnoreBinds(this) || hasAbility(player, Tailwind.class)) {
			return;
		}

    setFields();
		start();
	}
	
	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBPAMethods.getLong("Combos.Tailwind.Cooldown", currentLevel);
    duration = TLBPAMethods.getLong("Combos.Tailwind.Duration", currentLevel);
    speed = TLBPAMethods.getInt("Combos.Tailwind.Speed", currentLevel) - 1;
	}

	@Override
	public void progress() {
		if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
			remove();
			return;
		}
		
		if (player.getLocation().getBlock().isLiquid() && player.getEyeLocation().getBlock().isLiquid()) {
			remove();
			return;
		}
		
		if (getStartTime() + duration < System.currentTimeMillis()) {
			remove();
			return;
		}
		
		player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 10, speed, true, false), true);
		playAirbendingParticles(player.getEyeLocation(), 3, 0.3, 0.4, 0.3);
		playAirbendingParticles(player.getLocation().clone().add(0, 0.6, 0), 4, 0.2, 0.5, 0.2);
	}
	
	@Override
	public void remove() {
		super.remove();
		bPlayer.addCooldown(this);
	}

	@Override
	public boolean isSneakAbility() {
		return false;
	}

	@Override
	public boolean isHarmlessAbility() {
		return true;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "Tailwind";
	}

	@Override
	public Location getLocation() {
		return player.getLocation();
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public Object createNewComboInstance(Player player) {
		return new Tailwind(player);
	}

	@Override
	public ArrayList<AbilityInformation> getCombination() {
		ArrayList<AbilityInformation> combo = new ArrayList<>();
		
		combo.add(new AbilityInformation("AirBlast", ClickType.SHIFT_DOWN));
		combo.add(new AbilityInformation("AirBlast", ClickType.SHIFT_UP));
		combo.add(new AbilityInformation("AirBlast", ClickType.SHIFT_DOWN));
		combo.add(new AbilityInformation("AirBlast", ClickType.SHIFT_UP));
		combo.add(new AbilityInformation("AirBlast", ClickType.LEFT_CLICK));
		
		return combo;
	}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Combos.Tailwind.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "Create a tailwind behind you to increase your speed immensely!";
	}
	
	@Override
	public String getInstructions() {
		return "AirBlast (Double Tap Sneak) > AirBlast (Left Click)";
	}
}
