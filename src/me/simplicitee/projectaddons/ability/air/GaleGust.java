package me.simplicitee.projectaddons.ability.air;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.MainHand;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.object.HorizontalVelocityTracker;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class GaleGust extends AirAbility implements AddonAbility {
	private Location current;
	private long cooldown;
	private Vector direction;
	private double knockback;
	private double radius;
	private double damage;
	private double range;
	private Set<Point> points;

	public GaleGust(Player player) {
		super(player);
		
		if (CoreAbility.getAbility(this.getClass()) == null) {
			return;
		}
    setFields();
		if (player.getMainHand() == MainHand.LEFT) {
			current = GeneralMethods.getLeftSide(player.getLocation().clone().add(0, 1.2, 0), 0.55);
		} else {
			current = GeneralMethods.getRightSide(player.getLocation().clone().add(0, 1.2, 0), 0.55);
		}
		
		this.direction = player.getEyeLocation().getDirection().clone().normalize();
		this.points = new HashSet<>();

		start();
		bPlayer.addCooldown(this);
	}
	
	private void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBPAMethods.getLong("Abilities.GaleGust.Cooldown", currentLevel);
    this.knockback = TLBPAMethods.getDouble("Abilities.GaleGust.Knockback", currentLevel);
    this.radius = TLBPAMethods.getDouble("Abilities.GaleGust.Radius", currentLevel);
    this.damage = TLBPAMethods.getDouble("Abilities.GaleGust.Damage", currentLevel);
		this.range = TLBPAMethods.getLong("Abilities.GaleGust.Range", currentLevel);
	}

	@Override
	public void progress() {
		if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
			remove();
			return;
		}
		
		if (player.getLocation().distance(current) > range) {
			remove();
			return;
		}
		
		if (player.isSneaking()) {
			direction.add(player.getEyeLocation().getDirection()).normalize();
		}
		
		current = current.add(direction);
		
		if (!current.getBlock().isPassable()) {
			remove();
			return;
		}
		
		
		
		points.add(new Point(current.clone().setDirection(direction.clone())));
		
		for (Entity e : GeneralMethods.getEntitiesAroundPoint(current, radius)) {
			if (e.getEntityId() == player.getEntityId()) {
				continue;
			}
			
			if (e.isDead()) {
				remove();
				return;
			}
			
			if (e instanceof LivingEntity && damage > 0) {
				DamageHandler.damageEntity(e, damage, this);
				remove();
				return;
			}
			
			e.setVelocity(direction.multiply(knockback));
			new HorizontalVelocityTracker(e, player, 0, this);
			e.setFireTicks(0);
		}
		
		List<Point> remove = new ArrayList<>();
		
		for (Point point : points) {
			double radi = point.getRadius() + 0.12;
			
			if (radi > radius) {
				remove.add(point);
				continue;
			}
			
			for (int i = 0; i < 3; i++) {
				Vector ortho = GeneralMethods.getOrthogonalVector(point.getLocation().getDirection(), 120 * i + point.getAngle(), point.getRadius());
				playAirbendingParticles(point.getLocation().clone().add(ortho), 1, 0, 0, 0);
			}
			
			point.setAngle(point.getAngle() + Math.random() * 60).setRadius(radi);
		}
		
		points.removeAll(remove);
		
		if (points.isEmpty()) {
			remove();
			return;
		}
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "GaleGust";
	}

	@Override
	public Location getLocation() {
		return current;
	}

	@Override 
	public void load() {}

	@Override 
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	public class Point {
		
		private Location point;
		private double radius, angle;
		
		private Point(Location point) {
			this.point = point;
			this.radius = 0;
			this.angle = 0;
		}
		
		public Location getLocation() {
			return point;
		}
		
		public double getRadius() {
			return radius;
		}
		
		public double getAngle() {
			return angle;
		}
		
		public Point setRadius(double radius) {
			this.radius = radius;
			return this;
		}
		
		public Point setAngle(double angle) {
			this.angle = angle;
			return this;
		}
	}
	
	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.GaleGust.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "Create a very strong and sudden wind to blow away entities!";
	}
	
	@Override
	public String getInstructions() {
		return "Left Click, Sneak to control";
	}
}
