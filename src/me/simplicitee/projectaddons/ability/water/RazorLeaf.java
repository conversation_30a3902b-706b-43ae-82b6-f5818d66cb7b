package me.simplicitee.projectaddons.ability.water;

import java.util.EnumSet;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class RazorLeaf extends PlantAbility implements AddonAbility {
	
	private Location center;
	private int particles;
	private long cooldown;
	private double damage;
	private double radius;
	private double range;
	private double sourceRange;
	private TempBlock source;
  private long duration;

  private final Set<Material> leaves = EnumSet.of(
    Material.OAK_LEAVES, Material.SPRUCE_LEAVES, Material.DARK_OAK_LEAVES, Material.JUNGLE_LEAVES,
    Material.BIRCH_LEAVES, Material.ACACIA_LEAVES, Material.MANGROVE_LEAVES, Material.AZALEA_LEAVES, Material.FLOWERING_AZALEA_LEAVES,
    Material.OAK_SAPLING, Material.SPRUCE_SAPLING, Material.DARK_OAK_SAPLING, Material.JUNGLE_SAPLING,
    Material.BIRCH_SAPLING, Material.ACACIA_SAPLING, Material.MANGROVE_PROPAGULE,
    Material.SHORT_GRASS, Material.TALL_GRASS, Material.FERN, Material.LARGE_FERN, Material.LILY_PAD,
    Material.VINE, Material.PEONY, Material.ROSE_BUSH, Material.LILAC
  );
		
	public RazorLeaf(Player player, boolean sourced) {
		super(player);
		
		if (hasAbility(player, RazorLeaf.class)) {
			return;
		}
		setFields();
		if (sourced) {
			Block source = player.getTargetBlock(null, NumberConversions.ceil(sourceRange));

			if (!isPlantbendable(source.getType()) || !leaves.contains(source.getType())) {
				if (source.getType() != Material.MOSS_BLOCK) {
					return;
				}
			}
			
			this.source = new TempBlock(source, Material.AIR);
			this.center = source.getLocation().clone().add(0.5, 0.5, 0.5);
		} else {
			this.source = null;
			this.center = player.getEyeLocation().clone().add(player.getEyeLocation().getDirection().clone().normalize().multiply(1.5));
		}
		start();
	}

  private void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBPAMethods.getLong("Abilities.RazorLeaf.Cooldown", currentLevel);
    this.damage = TLBPAMethods.getDouble("Abilities.RazorLeaf.Damage", currentLevel);
    this.radius = TLBPAMethods.getDouble("Abilities.RazorLeaf.Radius", currentLevel);
    this.range = TLBPAMethods.getDouble("Abilities.RazorLeaf.Range", currentLevel);
    this.sourceRange = TLBPAMethods.getDouble("Abilities.RazorLeaf.SourceRange", currentLevel);
    this.particles = TLBPAMethods.getInt("Abilities.RazorLeaf.Particles", currentLevel);
    this.duration = TLBPAMethods.getInt("Abilities.RazorLeaf.Duration", currentLevel);
  }
  
	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead() || !bPlayer.canBendIgnoreBinds(getAbility("RazorLeaf"))) {
			remove();
			return;
		}
		
		if (!player.getWorld().equals(center.getWorld()) || !bPlayer.canBend(this)) {
			remove();
			return;
		}
		
		if (center.distance(player.getEyeLocation()) >= range) {
			remove();
			return;
		}
		
		if (System.currentTimeMillis() >= this.getStartTime() + this.duration) {
			remove();
			return;
		}

    Vector direction;
    if (player.isSneaking()) {
			Location holding = player.getEyeLocation().clone().add(player.getEyeLocation().getDirection().clone().normalize().multiply(1.5));
			direction = GeneralMethods.getDirection(center, holding);
		} else {
			Location target;
			Entity e = GeneralMethods.getTargetedEntity(player, range);
			if (e instanceof LivingEntity living) {
        target = living.getEyeLocation();
			} else {
        target = GeneralMethods.getTargetedLocation(player, range);
      }
			
			direction = GeneralMethods.getDirection(center, target);
		}
		
		if (direction.length() > 1) {
			center = center.add(direction.normalize().multiply(0.75));
		} else {
			center = center.add(direction);
		}
		
		if (!center.getBlock().isPassable()) {
			remove();
			return;
		}
		
		playPlantbendingSound(center);
		
		for (int n = 0; n < particles; n++) {
			Location current, start = center.clone();
			double c = 0.075;
			double phi = n * 137.5;
			double r = c * Math.sqrt(n);
			double x = r * Math.cos(Math.toRadians(phi));
			double z = r * Math.sin(Math.toRadians(phi));
			current = start.clone().add(x, 0, z);
			
			if (current.distance(start) > radius) {
				break;
			}
			
			GeneralMethods.displayColoredParticle("3D9970", current);
		}
		
		for (Entity e : GeneralMethods.getEntitiesAroundPoint(center, radius + 1)) {
			if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId()) {
				DamageHandler.damageEntity(e, damage, this);
				remove();
				return;
			}
		}
	}
	
	@Override
	public void remove() {
		super.remove();
		
		if (source != null) {
			if (source.getBlock().getType() == Material.AIR) {
				source.revertBlock();
			}
		}
		
		bPlayer.addCooldown(this);
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "RazorLeaf";
	}

	@Override
	public Location getLocation() {
		return center;
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}
	
	@Override
	public String getDescription() {
		return "Spin leaves around really fast and make them razor sharp!";
	}
	
	@Override
	public String getInstructions() {
		return "Sneak at plants to begin, hold to aim, and release to shoot it! Sneaking again will pull it back towards you!";
	}

	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Abilities.RazorLeaf.Enabled");
	}
}
